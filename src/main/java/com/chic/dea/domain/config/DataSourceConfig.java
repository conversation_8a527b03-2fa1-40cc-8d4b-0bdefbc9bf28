package com.chic.dea.domain.config;

import com.alibaba.druid.pool.DruidDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * 数据源配置类
 * 确保Spring Batch使用正确的主数据源
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Configuration
public class DataSourceConfig {

    /**
     * 主数据源配置 - 用于Spring Batch
     * 明确配置主数据源，避免在多数据源环境下选择错误的数据源
     */
    @Bean("batchDataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.dynamic.datasource.data-extract-audit")
    public DataSource batchDataSource() {
        log.info("配置Spring Batch专用主数据源(MySQL)");
        DruidDataSource dataSource = new DruidDataSource();
        // 基本配置会通过@ConfigurationProperties自动注入
        return dataSource;
    }
}
